#include "CIDQueueManager.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "NSMySQLConnection.h"
#include "Threading/ThreadedWorkManager.h"
#include "ConnectionManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "Diagnostics/NSLogger.h"
#include "NSDefine.h"
#include "AsyncQueryExecutor.h"
#include "GameThreadCallback.h"
#include <mysql.h>

namespace Database
{

QueryTimer::QueryTimer(std::shared_ptr<NSQueryData> data, 
                       std::function<void(const std::shared_ptr<NSQueryData>&)> callback)
    : m_start(std::chrono::high_resolution_clock::now())
    , m_queryData(std::move(data))
    , m_callback(std::move(callback))
{
}

QueryTimer::~QueryTimer()
{
    auto elapsed = std::chrono::high_resolution_clock::now() - m_start;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    if (m_queryData)
    {
        m_queryData->SetElapsedTime(elapsedMs);
        
        if (m_callback)
        {
            try
            {
                m_callback(m_queryData);
            }
            catch (const std::exception& e)
            {
                LOGE << "Exception in QueryTimer callback: " << e.what();
            }
        }
    }
}

CIDQueueManager::CIDQueueManager()
{
}

CIDQueueManager::~CIDQueueManager()
{
    // 폴링 스레드 종료
    m_pollingRunning = false;
    if (m_pollingThread.joinable())
    {
        m_pollingThread.join();
    }
    
    LOGD << "CIDQueueManager destroyed. Total queries processed: " << m_totalQueriesProcessed.load()
         << ", Total enqueued: " << m_totalQueriesEnqueued.load();
}

void CIDQueueManager::Initialize(ThreadedWorkManager* workerManager,
                                ConnectionManager* connectionManager,
                                std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback)
{
    m_workerManager = workerManager;
    m_connectionManager = connectionManager;
    m_afterExecuteCallback = std::move(afterExecuteCallback);
    
    m_asyncExecutor = std::make_unique<AsyncQueryExecutor>();
    
    // 폴링 스레드 시작
    m_pollingRunning = true;
    m_pollingThread = std::thread([this]() {
        mysql_thread_init(); // 스레드별 초기화
        
        while (m_pollingRunning)
        {
            PollActiveQueries();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        mysql_thread_end();
    });
}

void CIDQueueManager::EnqueueQuery(int64_t cid, QueryTask task)
{
    if (!m_workerManager || !m_connectionManager)
    {
        LOGE << "CIDQueueManager not properly initialized";
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("CIDQueueManager not initialized")));
        return;
    }

    task.cid = cid;
    m_totalQueriesEnqueued.fetch_add(1);

    auto cidQueue = GetOrCreateQueue(cid);
    
    bool shouldStartProcessing = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        bool wasEmpty = cidQueue->queries.empty();
        cidQueue->queries.push(std::move(task));
        
        if (wasEmpty && !cidQueue->isProcessing.exchange(true))
        {
            shouldStartProcessing = true;
        }
    }
    
    if (shouldStartProcessing)
    {
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
}

std::shared_ptr<CIDQueueManager::CIDQueue> CIDQueueManager::GetOrCreateQueue(int64_t cid)
{
    {
        std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            return it->second;
        }
    }
    
    std::unique_lock<std::shared_mutex> writeLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        return it->second;
    }
    
    auto newQueue = std::make_shared<CIDQueue>();
    m_cidQueues[cid] = newQueue;
    return newQueue;
}

void CIDQueueManager::ProcessQueue(int64_t cid)
{
    auto cidQueue = GetCIDQueue(cid);
    if (!cidQueue)
    {
        LOGE << "CID queue not found for CID: " << cid;
        return;
    }
    
    while (true)
    {
        QueryTask task;
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            if (cidQueue->queries.empty())
            {
                cidQueue->isProcessing.store(false);
                if (cidQueue->queries.empty()) break;
                cidQueue->isProcessing.store(true);
            }
            
            task = std::move(cidQueue->queries.front());
            cidQueue->queries.pop();
        }
        
        // 비동기로 실행
        StartAsyncQuery(task);
        
        // 즉시 다음 CID 처리로 이동 (블로킹하지 않음!)
        return;
                if (!cidQueue->isProcessing.exchange(true))
                {
                    continue;
                }
            }
            
            task = std::move(cidQueue->queries.front());
            cidQueue->queries.pop();
        }
        
        try
        {
            ExecuteQuery(task);
            m_totalQueriesProcessed.fetch_add(1);
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception executing query for CID " << cid << ": " << e.what();
            task.promise.SetException(std::current_exception());
        }
    }
}

void CIDQueueManager::ExecuteQuery(const QueryTask& task)
{
    auto conn = m_connectionManager->GetNextConnection();
    if (!conn)
    {
        throw std::runtime_error("Failed to get database connection");
    }
    
    // RAII pattern for timing and callback
    {
        QueryTimer timer(task.queryData, m_afterExecuteCallback);
        
        // Execute the stored procedure using the provided function
        if (task.executeFunc)
        {
            task.executeFunc(conn.get(), task.serializer, task.queryData);
        }
        else
        {
            LOGE << "No execute function provided for procedure: " << task.procedureName;
            task.queryData->SetErrorCode(EErrorCode::InvalidParameter);
        }
    }
    
    // Set the promise value to notify the waiting code
    task.promise.SetValue(task.queryData);
}

int64_t CIDQueueManager::ExtractCIDFromSerializer(const NSDataSerializer& serializer)
{
    // This is a placeholder - actual implementation would extract CID from serializer
    // based on the specific protocol/format used
    // For now, assuming CID is the first int64_t parameter
    int64_t cid = 0;
    // serializer.Read(cid); // Pseudo-code
    return cid;
}

size_t CIDQueueManager::GetQueueCount() const
{
    std::shared_lock<std::shared_mutex> lock(m_mapMutex);
    return m_cidQueues.size();
}

size_t CIDQueueManager::GetQueueSize(int64_t cid) const
{
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        std::lock_guard<std::mutex> queueLock(it->second->mutex);
        return it->second->queries.size();
    }
    return 0;
}

void CIDQueueManager::GetQueueStats(std::unordered_map<int64_t, size_t>& stats) const
{
    stats.clear();
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    
    for (const auto& [cid, queue] : m_cidQueues)
    {
        std::lock_guard<std::mutex> queueLock(queue->mutex);
        if (!queue->queries.empty())
        {
            stats[cid] = queue->queries.size();
        }
    }
}

std::shared_ptr<CIDQueueManager::CIDQueue> CIDQueueManager::GetCIDQueue(int64_t cid)
{
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        return it->second;
    }
    return nullptr;
}

void CIDQueueManager::StartAsyncQuery(const QueryTask& task)
{
    auto conn = m_connectionManager->GetNextConnection();
    if (!conn)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get connection")));
        return;
    }
    
    AsyncQueryTask asyncTask;
    asyncTask.cid = task.cid;
    asyncTask.connection = conn;
    asyncTask.callback = [this, task](bool success, MYSQL_RES* result) {
        if (success)
        {
            // RecordSet 생성 및 promise 완료
            task.queryData->SetResult(result);
            task.promise.SetValue(task.queryData);
            
            // 게임 스레드로 결과 전달
            PostToGameThread(task.cid, task.queryData);
        }
        else
        {
            task.promise.SetException(std::make_exception_ptr(
                std::runtime_error("Query execution failed")));
        }
        
        // 다음 작업 확인
        CheckNextTaskForCID(task.cid);
    };
    
    // 쿼리 실행 준비
    if (task.executeFunc)
    {
        // PreparedStatement 설정
        // ... stmt 준비 코드 ...
        asyncTask.isStmtQuery = true;
        // asyncTask.stmt = stmt;
    }
    else
    {
        // Serializer를 통한 쿼리 생성은 실제 구현에 맞게 수정 필요
        // asyncTask.query = task.serializer.GetQuery();
        asyncTask.query = task.procedureName; // 임시 구현
    }
    
    // 비동기 실행 시작
    if (m_asyncExecutor->StartAsyncQuery(asyncTask))
    {
        std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
        m_activeQueries.push_back(std::move(asyncTask));
    }
}

void CIDQueueManager::PollActiveQueries()
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    
    for (auto it = m_activeQueries.begin(); it != m_activeQueries.end();)
    {
        auto& task = *it;
        
        switch (task.state)
        {
        case AsyncQueryState::Executing:
            if (m_asyncExecutor->ContinueAsyncQuery(task))
            {
                // 상태가 변경됨
            }
            break;
            
        case AsyncQueryState::WaitingResult:
            if (m_asyncExecutor->FetchAsyncResult(task))
            {
                // 완료 처리
                ProcessCompletedQuery(task);
                it = m_activeQueries.erase(it);
                continue;
            }
            break;
            
        case AsyncQueryState::Completed:
        case AsyncQueryState::Failed:
            ProcessCompletedQuery(task);
            it = m_activeQueries.erase(it);
            continue;
        }
        
        ++it;
    }
}

void CIDQueueManager::ProcessCompletedQuery(AsyncQueryTask& asyncTask)
{
    m_totalQueriesProcessed.fetch_add(1);
    
    if (asyncTask.callback)
    {
        bool success = (asyncTask.state == AsyncQueryState::Completed);
        asyncTask.callback(success, asyncTask.result);
    }
    
    // Connection 반환 처리 (ConnectionWrapper가 자동으로 처리)
    if (asyncTask.connection)
    {
        // ConnectionWrapper의 inUse flag를 false로 설정
        // 이는 shared_ptr이 소멸될 때 자동으로 처리됨
    }
}

void CIDQueueManager::CheckNextTaskForCID(int64_t cid)
{
    auto cidQueue = GetCIDQueue(cid);
    if (!cidQueue)
        return;
    
    bool hasMore = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        hasMore = !cidQueue->queries.empty();
    }
    
    if (hasMore)
    {
        // 다음 작업을 워커에 할당
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
}

void CIDQueueManager::PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData)
{
    // 게임 스레드로 결과 전달
    GameThreadCallback::PostToGameThread(nullptr, [this, queryData]() {
        if (m_afterExecuteCallback)
        {
            m_afterExecuteCallback(queryData);
        }
    });
}

} // namespace Database