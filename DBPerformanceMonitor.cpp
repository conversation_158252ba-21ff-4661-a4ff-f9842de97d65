#include "DBPerformanceMonitor.h"
#include "Diagnostics/NSLogger.h"
#include <sstream>

namespace Database
{

DBPerformanceMonitor& DBPerformanceMonitor::GetInstance()
{
    static DBPerformanceMonitor instance;
    return instance;
}

void DBPerformanceMonitor::RecordQueryStart(int64_t cid, const std::string& query)
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    m_activeQueries[cid] = { query, std::chrono::steady_clock::now() };
}

void DBPerformanceMonitor::RecordQueryComplete(int64_t cid, bool success)
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    
    auto it = m_activeQueries.find(cid);
    if (it == m_activeQueries.end())
        return;
    
    auto elapsed = std::chrono::steady_clock::now() - it->second.startTime;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    // 통계 업데이트
    {
        std::lock_guard<std::mutex> statsLock(m_statsMutex);
        auto& stats = m_queryStats[it->second.query];
        stats.count.fetch_add(1);
        stats.totalTimeMs.fetch_add(elapsedMs);
        if (!success)
        {
            stats.failures.fetch_add(1);
        }
    }
    
    m_activeQueries.erase(it);
}

void DBPerformanceMonitor::LogStatistics()
{
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    if (m_queryStats.empty())
    {
        LOGI << "No query statistics available";
        return;
    }
    
    std::stringstream ss;
    ss << "\n=== DB Performance Statistics ===\n";
    
    for (const auto& [query, stats] : m_queryStats)
    {
        uint64_t count = stats.count.load();
        uint64_t totalMs = stats.totalTimeMs.load();
        uint64_t failures = stats.failures.load();
        
        if (count > 0)
        {
            double avgMs = static_cast<double>(totalMs) / count;
            double failureRate = (static_cast<double>(failures) / count) * 100.0;
            
            ss << "Query: " << query << "\n"
               << "  Count: " << count << "\n"
               << "  Avg Time: " << avgMs << " ms\n"
               << "  Total Time: " << totalMs << " ms\n"
               << "  Failures: " << failures << " (" << failureRate << "%)\n\n";
        }
    }
    
    ss << "=================================";
    LOGI << ss.str();
}

} // namespace Database