# Database_Maria 비동기 설계 구현을 위한 필수 변경사항

## 📌 현재 상태와 목표

### 현재 상태
- **블로킹 방식**: 워커가 쿼리 완료까지 대기
- **CID당 하나의 워커 고정**: 워커가 특정 CID에 묶여있음
- **커넥션 활용도 낮음**: 32개 커넥션 중 활성 워커 수만큼만 사용

### 목표 상태
- **논블로킹 방식**: 워커가 쿼리 전송 후 즉시 다른 작업 처리
- **동적 워커 할당**: 모든 워커가 모든 CID 처리 가능
- **커넥션 최대 활용**: 16개 워커가 32개 커넥션 모두 활용

## 🔧 필수 변경사항

### 1. MariaDB 라이브러리 초기화 추가

**위치**: `NSDataBaseManager.cpp`

```cpp
// NSDataBaseManager 생성자에 추가
NSDataBaseManager::NSDataBaseManager()
{
    // MariaDB 라이브러리 초기화 (프로세스당 1회)
    if (mysql_library_init(0, NULL, NULL) != 0)
    {
        LOGE << "Failed to initialize MySQL library";
        throw std::runtime_error("MySQL library initialization failed");
    }
}

// NSDataBaseManager 소멸자에 추가
NSDataBaseManager::~NSDataBaseManager()
{
    // ... 기존 정리 코드 ...
    
    // MariaDB 라이브러리 정리
    mysql_library_end();
}
```

**위치**: `Threading/AsyncDBWorker.cpp` 또는 `Threading/ThreadedWorkManager.cpp`

```cpp
// 각 워커 스레드 시작 부분에 추가
void WorkerThread()
{
    // 스레드별 MySQL 초기화
    mysql_thread_init();
    
    try
    {
        // ... 워커 로직 ...
    }
    catch (...)
    {
        // ... 예외 처리 ...
    }
    
    // 스레드 종료 시 정리
    mysql_thread_end();
}
```

### 2. 비동기 쿼리 실행 구현

**새 파일 생성**: `AsyncQueryExecutor.h`

```cpp
#pragma once
#include <functional>
#include <mysql.h>
#include <atomic>
#include <chrono>

namespace Database
{

// 비동기 쿼리 상태
enum class AsyncQueryState
{
    Pending,
    Executing,
    WaitingResult,
    Completed,
    Failed
};

// 비동기 쿼리 작업
struct AsyncQueryTask
{
    int64_t cid;
    std::string query;
    std::shared_ptr<NSMySQLConnection> connection;
    std::function<void(bool success, MYSQL_RES* result)> callback;
    
    AsyncQueryState state = AsyncQueryState::Pending;
    int asyncStatus = 0;
    std::chrono::steady_clock::time_point startTime;
    
    // 비동기 실행 컨텍스트
    bool isStmtQuery = false;
    MYSQL_STMT* stmt = nullptr;
    MYSQL_RES* result = nullptr;
};

class AsyncQueryExecutor
{
public:
    // 비동기 쿼리 시작
    bool StartAsyncQuery(AsyncQueryTask& task);
    
    // 진행 중인 쿼리 상태 확인 및 진행
    bool ContinueAsyncQuery(AsyncQueryTask& task);
    
    // 결과 가져오기
    bool FetchAsyncResult(AsyncQueryTask& task);
    
private:
    // 소켓 대기 상태 확인
    int GetWaitStatus(MYSQL* mysql);
};

} // namespace Database
```

**새 파일 생성**: `AsyncQueryExecutor.cpp`

```cpp
#include "AsyncQueryExecutor.h"
#include "NSMySQLConnection.h"
#include "Diagnostics/NSLogger.h"

namespace Database
{

bool AsyncQueryExecutor::StartAsyncQuery(AsyncQueryTask& task)
{
    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql) return false;
    
    task.startTime = std::chrono::steady_clock::now();
    
    // 비동기 쿼리 시작
    if (task.isStmtQuery && task.stmt)
    {
        // PreparedStatement 비동기 실행
        task.asyncStatus = mysql_stmt_execute_start(&task.asyncStatus, task.stmt);
    }
    else
    {
        // 일반 쿼리 비동기 실행
        task.asyncStatus = mysql_real_query_start(&task.asyncStatus, mysql, 
                                                   task.query.c_str(), 
                                                   task.query.length());
    }
    
    if (task.asyncStatus == 0)
    {
        // 즉시 완료됨 (로컬 DB 등)
        task.state = AsyncQueryState::Completed;
        return true;
    }
    
    task.state = AsyncQueryState::Executing;
    return true;
}

bool AsyncQueryExecutor::ContinueAsyncQuery(AsyncQueryTask& task)
{
    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql) return false;
    
    int wait_status = GetWaitStatus(mysql);
    
    if (task.isStmtQuery && task.stmt)
    {
        task.asyncStatus = mysql_stmt_execute_cont(&task.asyncStatus, task.stmt, wait_status);
    }
    else
    {
        task.asyncStatus = mysql_real_query_cont(&task.asyncStatus, mysql, wait_status);
    }
    
    if (task.asyncStatus == 0)
    {
        // 쿼리 실행 완료
        task.state = AsyncQueryState::WaitingResult;
        return true;
    }
    
    return false; // 아직 진행 중
}

bool AsyncQueryExecutor::FetchAsyncResult(AsyncQueryTask& task)
{
    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql) return false;
    
    if (task.isStmtQuery && task.stmt)
    {
        // PreparedStatement 결과 저장
        if (mysql_stmt_store_result(task.stmt) == 0)
        {
            task.result = mysql_stmt_result_metadata(task.stmt);
            task.state = AsyncQueryState::Completed;
            return true;
        }
    }
    else
    {
        // 일반 쿼리 결과 저장
        task.result = mysql_store_result(mysql);
        task.state = AsyncQueryState::Completed;
        return true;
    }
    
    task.state = AsyncQueryState::Failed;
    return false;
}

int AsyncQueryExecutor::GetWaitStatus(MYSQL* mysql)
{
    // Windows에서 소켓 상태 확인
    // 실제 구현은 mysql_get_socket()과 select() 사용
    return MYSQL_WAIT_READ | MYSQL_WAIT_WRITE;
}

} // namespace Database
```

### 3. CIDQueueManager 비동기 처리로 변경

**수정 파일**: `CIDQueueManager.h`

```cpp
// 추가할 멤버 변수
private:
    // 비동기 쿼리 실행기
    std::unique_ptr<AsyncQueryExecutor> m_asyncExecutor;
    
    // 진행 중인 비동기 작업들
    std::vector<AsyncQueryTask> m_activeQueries;
    std::mutex m_activeQueriesMutex;
    
    // 폴링 스레드
    std::thread m_pollingThread;
    std::atomic<bool> m_pollingRunning{false};
    
    // 새로운 메서드들
    void StartAsyncQuery(const QueryTask& task);
    void PollActiveQueries();
    void ProcessCompletedQuery(AsyncQueryTask& asyncTask);
```

**수정 파일**: `CIDQueueManager.cpp`

```cpp
void CIDQueueManager::Initialize(...)
{
    // ... 기존 초기화 코드 ...
    
    m_asyncExecutor = std::make_unique<AsyncQueryExecutor>();
    
    // 폴링 스레드 시작
    m_pollingRunning = true;
    m_pollingThread = std::thread([this]() {
        mysql_thread_init(); // 스레드별 초기화
        
        while (m_pollingRunning)
        {
            PollActiveQueries();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        mysql_thread_end();
    });
}

void CIDQueueManager::ProcessQueue(int64_t cid)
{
    auto cidQueue = GetCIDQueue(cid);
    
    while (true)
    {
        QueryTask task;
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            if (cidQueue->queries.empty())
            {
                cidQueue->isProcessing.store(false);
                if (cidQueue->queries.empty()) break;
                cidQueue->isProcessing.store(true);
            }
            
            task = std::move(cidQueue->queries.front());
            cidQueue->queries.pop();
        }
        
        // 비동기로 실행
        StartAsyncQuery(task);
        
        // 즉시 다음 CID 처리로 이동 (블로킹하지 않음!)
        return;
    }
}

void CIDQueueManager::StartAsyncQuery(const QueryTask& task)
{
    auto conn = m_connectionManager->GetNextConnection();
    if (!conn)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get connection")));
        return;
    }
    
    AsyncQueryTask asyncTask;
    asyncTask.cid = task.cid;
    asyncTask.connection = conn;
    asyncTask.callback = [this, task](bool success, MYSQL_RES* result) {
        if (success)
        {
            // RecordSet 생성 및 promise 완료
            task.queryData->SetResult(result);
            task.promise.SetValue(task.queryData);
            
            // 게임 스레드로 결과 전달
            PostToGameThread(task.cid, task.queryData);
        }
        else
        {
            task.promise.SetException(std::make_exception_ptr(
                std::runtime_error("Query execution failed")));
        }
        
        // 다음 작업 확인
        CheckNextTaskForCID(task.cid);
    };
    
    // 쿼리 실행 준비
    if (task.executeFunc)
    {
        // PreparedStatement 설정
        // ... stmt 준비 코드 ...
        asyncTask.isStmtQuery = true;
        asyncTask.stmt = stmt;
    }
    else
    {
        asyncTask.query = task.serializer.GetQuery();
    }
    
    // 비동기 실행 시작
    if (m_asyncExecutor->StartAsyncQuery(asyncTask))
    {
        std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
        m_activeQueries.push_back(std::move(asyncTask));
    }
}

void CIDQueueManager::PollActiveQueries()
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    
    for (auto it = m_activeQueries.begin(); it != m_activeQueries.end();)
    {
        auto& task = *it;
        
        switch (task.state)
        {
        case AsyncQueryState::Executing:
            if (m_asyncExecutor->ContinueAsyncQuery(task))
            {
                // 상태가 변경됨
            }
            break;
            
        case AsyncQueryState::WaitingResult:
            if (m_asyncExecutor->FetchAsyncResult(task))
            {
                // 완료 처리
                ProcessCompletedQuery(task);
                it = m_activeQueries.erase(it);
                continue;
            }
            break;
            
        case AsyncQueryState::Completed:
        case AsyncQueryState::Failed:
            ProcessCompletedQuery(task);
            it = m_activeQueries.erase(it);
            continue;
        }
        
        ++it;
    }
}

void CIDQueueManager::CheckNextTaskForCID(int64_t cid)
{
    auto cidQueue = GetCIDQueue(cid);
    
    bool hasMore = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        hasMore = !cidQueue->queries.empty();
    }
    
    if (hasMore)
    {
        // 다음 작업을 워커에 할당
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
}
```

### 4. ConnectionManager 스레드 안전성 추가

**수정 파일**: `ConnectionManager.h`

```cpp
// Connection 래퍼 추가
struct ConnectionWrapper
{
    std::shared_ptr<NSMySQLConnection> connection;
    std::mutex mutex;  // 커넥션별 뮤텍스
    std::atomic<bool> inUse{false};
};

private:
    std::vector<std::unique_ptr<ConnectionWrapper>> m_connections;
    
    // 사용 가능한 커넥션 찾기
    std::shared_ptr<NSMySQLConnection> GetAvailableConnection();
```

**수정 파일**: `ConnectionManager.cpp`

```cpp
std::shared_ptr<NSMySQLConnection> ConnectionManager::GetAvailableConnection()
{
    int attempts = 0;
    const int maxAttempts = m_connections.size() * 2;
    
    while (attempts < maxAttempts)
    {
        size_t index = m_nextConnection.fetch_add(1) % m_connections.size();
        auto& wrapper = m_connections[index];
        
        // Try-lock으로 사용 가능한 커넥션 찾기
        if (wrapper->mutex.try_lock())
        {
            std::lock_guard<std::mutex> lock(wrapper->mutex, std::adopt_lock);
            
            if (!wrapper->inUse.exchange(true))
            {
                // 커넥션 상태 확인
                if (wrapper->connection && wrapper->connection->IsConnected())
                {
                    return wrapper->connection;
                }
                
                // 재연결 시도
                if (!wrapper->connection)
                {
                    wrapper->connection = std::make_shared<NSMySQLConnection>();
                }
                
                if (wrapper->connection->Connect(...))
                {
                    return wrapper->connection;
                }
                
                wrapper->inUse = false;
            }
        }
        
        attempts++;
    }
    
    LOGW << "All connections are busy";
    return nullptr;
}
```

### 5. 게임 스레드로 결과 전달 구현

**새 파일 생성**: `GameThreadCallback.cpp`

```cpp
#include "GameThreadCallback.h"
#include <windows.h>

namespace Database
{

// Windows 메시지 정의
const UINT WM_DB_RESULT = WM_USER + 1001;

struct DBResultMessage
{
    int64_t cid;
    std::shared_ptr<NSQueryData> queryData;
    std::function<void()> callback;
};

// 전역 메시지 큐 (thread-safe)
concurrent_queue<DBResultMessage> g_gameThreadQueue;

void PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData)
{
    DBResultMessage msg;
    msg.cid = cid;
    msg.queryData = queryData;
    
    g_gameThreadQueue.push(msg);
    
    // 게임 스레드에 알림 (Windows 메시지 또는 다른 방법)
    // PostMessage(g_gameThreadHwnd, WM_DB_RESULT, 0, 0);
}

void ProcessGameThreadCallbacks()
{
    DBResultMessage msg;
    while (g_gameThreadQueue.try_pop(msg))
    {
        try
        {
            // 게임 로직 처리
            ProcessDBResult(msg.cid, msg.queryData);
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception in game thread callback: " << e.what();
        }
    }
}

} // namespace Database
```

### 6. 성능 모니터링 추가

**새 파일 생성**: `DBPerformanceMonitor.h`

```cpp
#pragma once
#include <atomic>
#include <chrono>
#include <unordered_map>
#include <mutex>

namespace Database
{

class DBPerformanceMonitor
{
public:
    static DBPerformanceMonitor& GetInstance();
    
    void RecordQueryStart(int64_t cid, const std::string& query);
    void RecordQueryComplete(int64_t cid, bool success);
    
    void LogStatistics();
    
private:
    struct QueryStats
    {
        std::atomic<uint64_t> count{0};
        std::atomic<uint64_t> totalTimeMs{0};
        std::atomic<uint64_t> failures{0};
    };
    
    std::unordered_map<std::string, QueryStats> m_queryStats;
    std::mutex m_statsMutex;
    
    // 활성 쿼리 추적
    struct ActiveQuery
    {
        std::string query;
        std::chrono::steady_clock::time_point startTime;
    };
    
    std::unordered_map<int64_t, ActiveQuery> m_activeQueries;
    std::mutex m_activeQueriesMutex;
};

} // namespace Database
```

## 📝 구현 순서

1. **MariaDB 초기화 추가** (즉시 적용 가능)
2. **ConnectionWrapper로 스레드 안전성 확보** (중요)
3. **AsyncQueryExecutor 구현**
4. **CIDQueueManager 비동기 전환**
5. **게임 스레드 콜백 통합**
6. **성능 모니터링 추가**

## ⚠️ 주의사항

1. **기존 코드 제거**: 즉시 적용으로 기존 코드 제거후 작업하거나 작업후 즉시 제거 코드 정리
2. **모니터링**: 성능 지표를 지속적으로 모니터링

## 🎯 기대 효과

- **처리량 증가**: 16개 워커가 32개 커넥션 모두 활용
- **응답 시간 단축**: 쿼리 대기 시간 제거
- **자원 효율성**: CPU 및 네트워크 활용도 극대화
- **확장성**: 더 많은 동시 사용자 처리 가능

이 변경사항들을 단계적으로 적용하면 고효율 비동기 DB 처리 시스템을 구축할 수 있습니다.