#include "../stdafx.h"
#include "ThreadedWorkManager.h"
#include <windows.h>
#include <processthreadsapi.h>
#include <mysql.h>

ThreadedWorkManager::ThreadedWorkManager()
{
}

ThreadedWorkManager::~ThreadedWorkManager()
{
    Finalize();
}

bool ThreadedWorkManager::Initialize(int workerCount)
{
    if (m_initialized)
        return false;

    if (workerCount <= 0 || workerCount > 64)
        return false;

    m_workers.reserve(workerCount);

    for (int i = 0; i < workerCount; ++i)
    {
        auto worker = std::make_unique<WorkerThread>();
        worker->name = "DBWorker#" + std::to_string(i);
        worker->running = true;
        
        worker->thread = std::thread([this, i]() { WorkerProc(i); });
        
        m_workers.push_back(std::move(worker));
    }

    m_initialized = true;
    return true;
}

void ThreadedWorkManager::Finalize()
{
    if (!m_initialized)
        return;

    // 모든 워커에 종료 신호
    for (auto& worker : m_workers)
    {
        worker->running = false;
        worker->cv.notify_one();
    }

    // 모든 워커 스레드 종료 대기
    for (auto& worker : m_workers)
    {
        if (worker->thread.joinable())
        {
            worker->thread.join();
        }
    }

    m_workers.clear();
    m_initialized = false;
}

void ThreadedWorkManager::PostWork(WorkFunction work, int threadIndex)
{
    if (!m_initialized || !work)
        return;

    if (threadIndex < 0 || threadIndex >= static_cast<int>(m_workers.size()))
    {
        threadIndex = threadIndex % m_workers.size();
    }

    auto& worker = m_workers[threadIndex];
    
    {
        std::lock_guard<std::mutex> lock(worker->queueMutex);
        worker->queue.push(std::move(work));
        worker->queueSize++;
    }
    
    worker->cv.notify_one();
}

void ThreadedWorkManager::PostWork(WorkFunction work)
{
    if (!m_initialized || !work)
        return;

    // 라운드 로빈 방식으로 워커 선택
    int index = m_nextWorkerIndex.fetch_add(1) % m_workers.size();
    PostWork(std::move(work), index);
}

int64_t ThreadedWorkManager::GetQueueSize(int threadIndex) const
{
    if (threadIndex < 0 || threadIndex >= static_cast<int>(m_workers.size()))
        return 0;

    return m_workers[threadIndex]->queueSize.load();
}

int64_t ThreadedWorkManager::GetTotalQueueSize() const
{
    int64_t total = 0;
    for (const auto& worker : m_workers)
    {
        total += worker->queueSize.load();
    }
    return total;
}

void ThreadedWorkManager::WorkerProc(int index)
{
    // 스레드별 MySQL 초기화
    mysql_thread_init();
    
    auto& worker = *m_workers[index];
    SetThreadName(worker.name);

    while (worker.running)
    {
        std::unique_lock<std::mutex> lock(worker.queueMutex);
        
        worker.cv.wait(lock, [&worker]() {
            return !worker.queue.empty() || !worker.running;
        });

        while (!worker.queue.empty())
        {
            auto work = std::move(worker.queue.front());
            worker.queue.pop();
            worker.queueSize--;

            lock.unlock();
            
            try
            {
                work();
                worker.processedCount++;
            }
            catch (...)
            {
                // 예외 로깅
            }
            
            lock.lock();
        }
    }
    
    // 스레드 종료 시 정리
    mysql_thread_end();
}

void ThreadedWorkManager::SetThreadName(const std::string& name)
{
#ifdef _WIN32
    const DWORD MS_VC_EXCEPTION = 0x406D1388;

    #pragma pack(push,8)
    typedef struct tagTHREADNAME_INFO
    {
        DWORD dwType;
        LPCSTR szName;
        DWORD dwThreadID;
        DWORD dwFlags;
    } THREADNAME_INFO;
    #pragma pack(pop)

    THREADNAME_INFO info;
    info.dwType = 0x1000;
    info.szName = name.c_str();
    info.dwThreadID = GetCurrentThreadId();
    info.dwFlags = 0;

    __try
    {
        RaiseException(MS_VC_EXCEPTION, 0, sizeof(info)/sizeof(ULONG_PTR), (ULONG_PTR*)&info);
    }
    __except(EXCEPTION_EXECUTE_HANDLER)
    {
    }
#endif
}