#include "AsyncQueryExecutor.h"
#include "Connection/NSMySQLConnection.h"
#include "Diagnostics/NSLogger.h"

namespace Database
{

bool AsyncQueryExecutor::StartAsyncQuery(AsyncQueryTask& task)
{
    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql) return false;
    
    task.startTime = std::chrono::steady_clock::now();
    
    // 비동기 쿼리 시작
    if (task.isStmtQuery && task.stmt)
    {
        // PreparedStatement 비동기 실행
        task.asyncStatus = mysql_stmt_execute_start(&task.asyncStatus, task.stmt);
    }
    else
    {
        // 일반 쿼리 비동기 실행
        task.asyncStatus = mysql_real_query_start(&task.asyncStatus, mysql, 
                                                   task.query.c_str(), 
                                                   task.query.length());
    }
    
    if (task.asyncStatus == 0)
    {
        // 즉시 완료됨 (로컬 DB 등)
        task.state = AsyncQueryState::Completed;
        return true;
    }
    
    task.state = AsyncQueryState::Executing;
    return true;
}

bool AsyncQueryExecutor::ContinueAsyncQuery(AsyncQueryTask& task)
{
    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql) return false;
    
    int wait_status = GetWaitStatus(mysql);
    
    if (task.isStmtQuery && task.stmt)
    {
        task.asyncStatus = mysql_stmt_execute_cont(&task.asyncStatus, task.stmt, wait_status);
    }
    else
    {
        task.asyncStatus = mysql_real_query_cont(&task.asyncStatus, mysql, wait_status);
    }
    
    if (task.asyncStatus == 0)
    {
        // 쿼리 실행 완료
        task.state = AsyncQueryState::WaitingResult;
        return true;
    }
    
    return false; // 아직 진행 중
}

bool AsyncQueryExecutor::FetchAsyncResult(AsyncQueryTask& task)
{
    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql) return false;
    
    if (task.isStmtQuery && task.stmt)
    {
        // PreparedStatement 결과 저장
        if (mysql_stmt_store_result(task.stmt) == 0)
        {
            task.result = mysql_stmt_result_metadata(task.stmt);
            task.state = AsyncQueryState::Completed;
            return true;
        }
    }
    else
    {
        // 일반 쿼리 결과 저장
        task.result = mysql_store_result(mysql);
        task.state = AsyncQueryState::Completed;
        return true;
    }
    
    task.state = AsyncQueryState::Failed;
    return false;
}

int AsyncQueryExecutor::GetWaitStatus(MYSQL* mysql)
{
    // Windows에서 소켓 상태 확인
    // 실제 구현은 mysql_get_socket()과 select() 사용
    return MYSQL_WAIT_READ | MYSQL_WAIT_WRITE;
}

} // namespace Database