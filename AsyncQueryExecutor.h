#pragma once
#include <functional>
#include <mysql.h>
#include <atomic>
#include <chrono>

namespace Database
{

// 비동기 쿼리 상태
enum class AsyncQueryState
{
    Pending,
    Executing,
    WaitingResult,
    Completed,
    Failed
};

// 비동기 쿼리 작업
struct AsyncQueryTask
{
    int64_t cid;
    std::string query;
    std::shared_ptr<NSMySQLConnection> connection;
    std::function<void(bool success, MYSQL_RES* result)> callback;
    
    AsyncQueryState state = AsyncQueryState::Pending;
    int asyncStatus = 0;
    std::chrono::steady_clock::time_point startTime;
    
    // 비동기 실행 컨텍스트
    bool isStmtQuery = false;
    MYSQL_STMT* stmt = nullptr;
    MYSQL_RES* result = nullptr;
};

class AsyncQueryExecutor
{
public:
    // 비동기 쿼리 시작
    bool StartAsyncQuery(AsyncQueryTask& task);
    
    // 진행 중인 쿼리 상태 확인 및 진행
    bool ContinueAsyncQuery(AsyncQueryTask& task);
    
    // 결과 가져오기
    bool FetchAsyncResult(AsyncQueryTask& task);
    
private:
    // 소켓 대기 상태 확인
    int GetWaitStatus(MYSQL* mysql);
};

} // namespace Database