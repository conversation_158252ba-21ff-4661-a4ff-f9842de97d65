#pragma once
#include <atomic>
#include <chrono>
#include <unordered_map>
#include <mutex>

namespace Database
{

class DBPerformanceMonitor
{
public:
    static DBPerformanceMonitor& GetInstance();
    
    void RecordQueryStart(int64_t cid, const std::string& query);
    void RecordQueryComplete(int64_t cid, bool success);
    
    void LogStatistics();
    
private:
    struct QueryStats
    {
        std::atomic<uint64_t> count{0};
        std::atomic<uint64_t> totalTimeMs{0};
        std::atomic<uint64_t> failures{0};
    };
    
    std::unordered_map<std::string, QueryStats> m_queryStats;
    std::mutex m_statsMutex;
    
    // 활성 쿼리 추적
    struct ActiveQuery
    {
        std::string query;
        std::chrono::steady_clock::time_point startTime;
    };
    
    std::unordered_map<int64_t, ActiveQuery> m_activeQueries;
    std::mutex m_activeQueriesMutex;
};

} // namespace Database