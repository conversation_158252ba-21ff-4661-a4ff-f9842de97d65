#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "Threading/ThreadedWorkManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include "CIDQueueManager.h"
#include "ConnectionManager.h"
#include <thread>
#include <sstream>
#include <mysql.h>
#include "Diagnostics/NSLogger.h"

// 스레드 로컬 시퀀스 스토리지 정의
thread_local NSDataBaseManager::ThreadLocalSequence NSDataBaseManager::t_localSequence;

NSDataBaseManager::NSDataBaseManager()
{
    // MariaDB 라이브러리 초기화 (프로세스당 1회)
    if (mysql_library_init(0, NULL, NULL) != 0)
    {
        LOGE << "Failed to initialize MySQL library";
        throw std::runtime_error("MySQL library initialization failed");
    }
}

NSDataBaseManager::~NSDataBaseManager()
{
    Finalize();
    
    // MariaDB 라이브러리 정리
    mysql_library_end();
}

bool NSDataBaseManager::Initialize()
{
    if (m_initialized.exchange(true))
        return true;

    try
    {
        // 스레드별 작업 매니저 생성
        m_workerManager = std::make_unique<ThreadedWorkManager>();
        
        // CID Queue 매니저 생성
        m_cidQueueManager = std::make_unique<Database::CIDQueueManager>();
        
        // Connection 매니저 생성
        m_connectionManager = std::make_unique<Database::ConnectionManager>();
        
        // 연결 풀은 AddConnectionInfo에서 생성됨

        return true;
    }
    catch (const std::exception& e)
    {
        m_initialized = false;
        return false;
    }
}

void NSDataBaseManager::Finalize()
{
    if (!m_initialized.exchange(false))
        return;

    Stop();

    // 연결 풀 종료
    for (auto& pool : m_connectionPools)
    {
        if (pool)
        {
            pool->Finalize();
            pool.reset();
        }
    }
}

bool NSDataBaseManager::AddConnectionInfo(int32 dbType, const std::string& host, int port,
                                         const std::string& dbName, const std::string& user, const std::string& password)
{
    auto* pool = GetConnectionPool(dbType);
    if (!pool)
        return false;
        
    return pool->AddConnectionInfo(host, port, dbName, user, password);
}

bool NSDataBaseManager::AddConnectionInfo(EDataBase dbType, EDBProvider provider, const std::string_view host, 
                                         uint32_t port, const std::string_view dbName, const std::string_view user, 
                                         const std::string_view password, int32_t initPoolCount)
{
    // provider 파라미터는 무시 (항상 MariaDB 사용)
    
    int32 dbTypeInt = static_cast<int32>(dbType);
    
    // 범위 체크
    if (dbTypeInt < static_cast<int32>(EDataBase::Game) || dbTypeInt >= static_cast<int32>(EDataBase::End))
        return false;
    
    // 기존 풀이 없으면 파라미터로 생성
    if (!m_connectionPools[dbTypeInt])
    {
        m_connectionPools[dbTypeInt] = std::make_unique<NSMySQLConnectionPool>(
            std::string(host), port, std::string(dbName), 
            std::string(user), std::string(password));
    }
    
    auto* pool = m_connectionPools[dbTypeInt].get();
    if (!pool)
        return false;
        
    // 풀 크기 설정
    pool->SetMaxConnections(initPoolCount);
    pool->SetMinConnections(std::min(5, initPoolCount));
    
    // 풀 초기화
    if (!pool->Initialize(dbTypeInt, 0))
        return false;
    
    return true;
}

NSMySQLConnectionPool* NSDataBaseManager::GetDBConnection(int32 dbType)
{
    return GetConnectionPool(dbType);
}

void NSDataBaseManager::ReconnectConnection(int32 dbType)
{
    auto* pool = GetConnectionPool(dbType);
    if (pool)
    {
        pool->Reconnect();
    }
}

int64_t NSDataBaseManager::GetDBQueueSize() const
{
    if (!m_workerManager)
        return 0;
    return m_workerManager->GetQueueSize();
}

std::string NSDataBaseManager::GetConnectionPoolCountInfo() const
{
    std::stringstream ss;
    for (int i = 0; i < 12; ++i)
    {
        if (m_connectionPools[i])
        {
            ss << "Pool[" << i << "]: " << m_connectionPools[i]->GetActiveConnectionCount() 
               << "/" << m_connectionPools[i]->GetTotalConnectionCount() << " ";
        }
    }
    return ss.str();
}

std::string NSDataBaseManager::GetConnectionPoolCountLog() const
{
    return GetConnectionPoolCountInfo(); // 동일한 정보 반환
}

NSMySQLConnectionPool* NSDataBaseManager::GetConnectionPool(int32 dbType)
{
    if (dbType >= static_cast<int32>(EDataBase::Game) && dbType < static_cast<int32>(EDataBase::End))
        return m_connectionPools[dbType].get();

    return nullptr;
}

// 시퀀스 관리 메서드 구현 (단순화)
int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    // 스레드 로컬 스토리지 사용 - 락 불필요
    return ++t_localSequence.sequenceByCid[cid];
}

void NSDataBaseManager::OnSessionClosed(int64_t cid)
{
    // 스레드 로컬 스토리지에서 제거 - 락 불필요
    t_localSequence.sequenceByCid.erase(cid);
}

template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    const Connection& connection,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // CID 추출
        int64_t cid = 0;
        if constexpr (requires { SP::Input; })
        {
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            if constexpr (requires { input.Cid; })
                cid = input.Cid;
            else if constexpr (requires { input.Aid; })
                cid = input.Aid;
        }
        
        m_queriesProcessing++;
        m_inputCount++;
        
        // QueryData 생성
        auto queryData = std::make_shared<NSQueryData>();
        queryData->SetProcedureName(SP::GetProcedureName());
        
        // QueryTask 생성
        Database::QueryTask task{
            .connection = connection,
            .serializer = serializer,
            .promise = promise,
            .queryData = queryData,
            .afterExecuteCallback = m_afterExecuteQueryShared,
            .procedureName = SP::GetProcedureName(),
            .cid = cid,
            .executeFunc = [](NSMySQLConnection* conn, const NSDataSerializer& ser, std::shared_ptr<NSQueryData> data) {
                SP sp;
                auto result = sp.Execute(conn, ser);
                data->SetErrorCode(result);
            }
        };
        
        // CID 큐에 추가
        m_cidQueueManager->EnqueueQuery(cid, std::move(task));
        
        // Note: The actual query execution is now handled by CIDQueueManager
        // The promise will be fulfilled when the query is processed

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
        
    // 스레드 수 결정
    if (workThreadCnt == 0)
    {
        // 전체 연결 수 계산
        int totalConnections = 0;
        for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
        {
            if (m_connectionPools[i])
            {
                totalConnections += m_connectionPools[i]->GetMaxConnections();
            }
        }
        
        // 연결갯수 나누기 2 (설계 문서대로)
        workThreadCnt = std::max(totalConnections / 2, 32);
    }
    
    m_threadCount = workThreadCnt;
    
    // IOCP 워커 매니저 시작
    if (!m_workerManager->Initialize(workThreadCnt))
        return false;
    
    // Connection Manager 초기화 (첫 번째 풀의 연결 정보 사용)
    for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
    {
        if (m_connectionPools[i])
        {
            auto connInfo = m_connectionPools[i]->GetConnectionInfo();
            int connCount = m_connectionPools[i]->GetMaxConnections();
            
            if (!m_connectionManager->Initialize(connInfo, connCount))
            {
                return false;
            }
            break; // 첫 번째 풀만 사용
        }
    }
    
    // CID Queue Manager 초기화
    m_cidQueueManager->Initialize(m_workerManager.get(), m_connectionManager.get(), m_afterExecuteQueryShared);
        
    // 연결 풀은 AddConnectionInfo에서 이미 초기화됨
    // 여기서는 모든 풀이 준비되었는지 확인만 함
    for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
    {
        if (m_connectionPools[i] && !m_connectionPools[i]->GetActiveConnections())
        {
            LOGW << "Connection pool " << i << " has no active connections";
        }
    }
        
    return true;
}

void NSDataBaseManager::Stop()
{
    ProhibitPushAccess();
    StopAllWorkerThreadAndWait();
}

void NSDataBaseManager::ProhibitPushAccess()
{
    m_pushAccessProhibit = true;
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
    // 모든 큐 작업이 완료될 때까지 대기
    while (m_pushAccessCount > 0 || m_queriesProcessing > 0)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 워커 매니저 종료
    if (m_workerManager)
    {
        m_workerManager->Finalize();
    }
}

DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StorageUpdateQuery(
    std::shared_ptr<NSStorageUpdateContainer> containerData,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
    std::shared_ptr<NSDBSession> session,
    std::source_location location)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
        
        // CID 기반 스레드 선택
        int threadIndex = GetExecutorByShardKey(containerData->Cid);
        
        m_queriesProcessing++;
        
        // 작업 큐에 추가 - CID 기반 스레드 선택
        m_workerManager->PostWork([=]() mutable
        {
            // RAII 가드로 atomic counter 보장
            struct CounterGuard {
                std::atomic<int>& queries;
                std::atomic<int>& access;
                ~CounterGuard() {
                    queries--;
                    access--;
                }
            } guard{m_queriesProcessing, m_pushAccessCount};
            
            try {
                // Query 실행
                EErrorCode queryResult = EErrorCode::None;
                if (pQueryFunc)
                {
                    queryResult = pQueryFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // Result 처리
                if (pResultFunc)
                {
                    queryResult = pResultFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);
                if (m_afterExecuteQueryShared)
                    m_afterExecuteQueryShared(queryData);
                
                promise.SetValue(queryData);
            }
            catch (const std::exception& e) {
                LOGE << "StorageUpdateQuery exception: " << e.what() 
                     << " at " << location.function_name() << ":" << location.line();
                promise.SetException(std::current_exception());
            }
            catch (...) {
                LOGE << "Unknown exception in StorageUpdateQuery"
                     << " at " << location.function_name() << ":" << location.line();
                promise.SetException(std::current_exception());
            }
        }, threadIndex);
    });
}