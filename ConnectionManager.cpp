#include "ConnectionManager.h"
#include "NSMySQLConnection.h"
#include "ConnectionInfo.h"
#include "Diagnostics/NSLogger.h"
#include <algorithm>

namespace Database
{

ConnectionManager::ConnectionManager()
{
}

ConnectionManager::~ConnectionManager()
{
    Shutdown();
}

bool ConnectionManager::Initialize(const ConnectionInfo& connInfo, int connectionCount)
{
    if (m_initialized.load())
    {
        LOGW << "ConnectionManager already initialized";
        return true;
    }

    if (connectionCount <= 0)
    {
        LOGE << "Invalid connection count: " << connectionCount;
        return false;
    }

    // Store connection info
    m_host = connInfo.host;
    m_port = connInfo.port;
    m_user = connInfo.user;
    m_password = connInfo.password;
    m_database = connInfo.database;
    m_charset = connInfo.charset;
    m_connectionFlags = connInfo.connectionFlags;

    // Create connections
    m_connections.reserve(connectionCount);
    
    for (int i = 0; i < connectionCount; ++i)
    {
        auto wrapper = std::make_unique<ConnectionWrapper>();
        wrapper->connection = std::make_shared<NSMySQLConnection>();
        
        if (!wrapper->connection->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                          m_password.c_str(), m_database.c_str(), 
                          m_charset.c_str(), m_connectionFlags))
        {
            LOGE << "Failed to create connection " << i << " to " 
                 << m_host << ":" << m_port << "/" << m_database;
            
            // Cleanup already created connections
            Shutdown();
            return false;
        }
        
        m_connections.push_back(std::move(wrapper));
        LOGD << "Created connection " << i << " to " << m_database;
    }

    m_initialized.store(true);
    LOGI << "ConnectionManager initialized with " << connectionCount << " connections";
    return true;
}

void ConnectionManager::Shutdown()
{
    if (!m_initialized.exchange(false))
    {
        return;
    }

    LOGD << "Shutting down ConnectionManager with " << m_connections.size() << " connections";
    
    // Close all connections
    for (auto& wrapper : m_connections)
    {
        if (wrapper && wrapper->connection)
        {
            wrapper->connection->Disconnect();
            wrapper->inUse = false;
        }
    }
    
    m_connections.clear();
    m_nextConnection.store(0);
    
    LOGI << "ConnectionManager shutdown complete. Total connections used: " 
         << m_totalConnectionsUsed.load();
}

std::shared_ptr<NSMySQLConnection> ConnectionManager::GetNextConnection()
{
    return GetAvailableConnection();
}

std::shared_ptr<NSMySQLConnection> ConnectionManager::GetAvailableConnection()
{
    if (!m_initialized.load() || m_connections.empty())
    {
        LOGE << "ConnectionManager not initialized or no connections available";
        return nullptr;
    }
    
    int attempts = 0;
    const int maxAttempts = m_connections.size() * 2;
    
    while (attempts < maxAttempts)
    {
        size_t index = m_nextConnection.fetch_add(1) % m_connections.size();
        auto& wrapper = m_connections[index];
        
        // Try-lock으로 사용 가능한 커넥션 찾기
        if (wrapper->mutex.try_lock())
        {
            std::lock_guard<std::mutex> lock(wrapper->mutex, std::adopt_lock);
            
            if (!wrapper->inUse.exchange(true))
            {
                // 커넥션 상태 확인
                if (wrapper->connection && wrapper->connection->IsConnected())
                {
                    m_totalConnectionsUsed.fetch_add(1);
                    return wrapper->connection;
                }
                
                // 재연결 시도
                if (!wrapper->connection)
                {
                    wrapper->connection = std::make_shared<NSMySQLConnection>();
                }
                
                if (wrapper->connection->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                                  m_password.c_str(), m_database.c_str(), 
                                  m_charset.c_str(), m_connectionFlags))
                {
                    m_totalConnectionsUsed.fetch_add(1);
                    return wrapper->connection;
                }
                
                wrapper->inUse = false;
            }
        }
        
        attempts++;
    }
    
    LOGW << "All connections are busy";
    return nullptr;
}

void ConnectionManager::GetConnectionStats(std::vector<std::pair<size_t, uint64_t>>& stats) const
{
    stats.clear();
    stats.reserve(m_connections.size());
    
    for (size_t i = 0; i < m_connections.size(); ++i)
    {
        if (m_connections[i] && m_connections[i]->connection)
        {
            // Report connection index and whether it's in use
            bool isConnected = m_connections[i]->connection->IsConnected();
            bool isInUse = m_connections[i]->inUse.load();
            stats.emplace_back(i, (isConnected ? 1 : 0) | (isInUse ? 2 : 0));
        }
    }
}

} // namespace Database